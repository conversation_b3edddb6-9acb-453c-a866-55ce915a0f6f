import PatientVisit from '../models/PatientVisit.js';
import Patient from '../models/Patient.js';
import User from '../models/User.js';

// Get all patient visits with filtering and pagination
export const getPatientVisits = async (req, res) => {
  try {
    const {
      limit = 20,
      page = 1,
      search,
      status,
      department,
      doctorId,
      patientId,
      startDate,
      endDate
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (status) filter.status = status;
    if (department) filter.department = department;
    if (doctorId) filter.doctor = doctorId;
    if (patientId) filter.patient = patientId;
    
    if (startDate || endDate) {
      filter.visitDate = {};
      if (startDate) filter.visitDate.$gte = new Date(startDate);
      if (endDate) filter.visitDate.$lte = new Date(endDate);
    }

    // Search functionality
    if (search) {
      const patients = await Patient.find({
        $or: [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { patientId: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');
      
      const patientIds = patients.map(p => p._id);
      filter.$or = [
        { patient: { $in: patientIds } },
        { visitId: { $regex: search, $options: 'i' } },
        { chiefComplaint: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const visits = await PatientVisit.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .sort({ visitDate: -1, visitTime: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await PatientVisit.countDocuments(filter);

    res.json({
      success: true,
      data: visits,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching patient visits:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch patient visits'
    });
  }
};

// Get patient visit statistics
export const getPatientVisitStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));
    
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Get various statistics
    const [
      totalVisits,
      todayVisits,
      weekVisits,
      monthVisits,
      scheduledVisits,
      inProgressVisits,
      completedVisits,
      cancelledVisits,
      emergencyVisits
    ] = await Promise.all([
      PatientVisit.countDocuments(),
      PatientVisit.countDocuments({ 
        visitDate: { $gte: startOfDay, $lte: endOfDay } 
      }),
      PatientVisit.countDocuments({ 
        visitDate: { $gte: startOfWeek } 
      }),
      PatientVisit.countDocuments({ 
        visitDate: { $gte: startOfMonth } 
      }),
      PatientVisit.countDocuments({ status: 'Scheduled' }),
      PatientVisit.countDocuments({ status: 'In Progress' }),
      PatientVisit.countDocuments({ status: 'Completed' }),
      PatientVisit.countDocuments({ status: 'Cancelled' }),
      PatientVisit.countDocuments({ visitType: 'Emergency' })
    ]);

    // Calculate average visit duration (mock calculation)
    const avgDuration = 45; // minutes

    // Get department-wise statistics
    const departmentStats = await PatientVisit.aggregate([
      {
        $group: {
          _id: '$department',
          count: { $sum: 1 },
          avgDuration: { $avg: '$visitDuration' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        totalVisits,
        todayVisits,
        weekVisits,
        monthVisits,
        scheduledVisits,
        inProgressVisits,
        completedVisits,
        cancelledVisits,
        emergencyVisits,
        avgDuration,
        departmentStats
      }
    });
  } catch (error) {
    console.error('Error fetching visit stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch visit statistics'
    });
  }
};

// Get single patient visit
export const getPatientVisitById = async (req, res) => {
  try {
    const visit = await PatientVisit.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email dateOfBirth gender')
      .populate('doctor', 'firstName lastName department specialization')
      .populate('createdBy', 'firstName lastName');

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    res.json({
      success: true,
      data: visit
    });
  } catch (error) {
    console.error('Error fetching patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch patient visit'
    });
  }
};

// Create new patient visit
export const createPatientVisit = async (req, res) => {
  try {
    const visitData = {
      ...req.body,
      createdBy: req.user?.id || req.body.createdBy
    };

    // Generate visit ID if not provided
    if (!visitData.visitId) {
      const count = await PatientVisit.countDocuments();
      visitData.visitId = `VIS${String(count + 1).padStart(6, '0')}`;
    }

    const visit = new PatientVisit(visitData);
    await visit.save();

    const populatedVisit = await PatientVisit.findById(visit._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    res.status(201).json({
      success: true,
      data: populatedVisit
    });
  } catch (error) {
    console.error('Error creating patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create patient visit'
    });
  }
};

// Update patient visit
export const updatePatientVisit = async (req, res) => {
  try {
    const visit = await PatientVisit.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    res.json({
      success: true,
      data: visit
    });
  } catch (error) {
    console.error('Error updating patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update patient visit'
    });
  }
};

// Delete patient visit
export const deletePatientVisit = async (req, res) => {
  try {
    const visit = await PatientVisit.findByIdAndDelete(req.params.id);

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    res.json({
      success: true,
      message: 'Patient visit deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete patient visit'
    });
  }
};

export {
  getPatientVisits,
  getPatientVisitStats,
  getPatientVisitById as getPatientVisit,
  createPatientVisit,
  updatePatientVisit,
  deletePatientVisit
};
