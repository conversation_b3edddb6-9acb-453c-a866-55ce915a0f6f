import React, { useState, useEffect } from 'react';
import { 
  UserPlus, 
  Calendar, 
  Clock, 
  Stethoscope, 
  FileText, 
  Pill, 
  CreditCard, 
  Home,
  CheckCircle,
  ArrowRight,
  AlertCircle,
  User,
  Activity,
  Heart,
  Thermometer,
  Eye,
  Edit,
  Plus
} from 'lucide-react';

interface PatientJourneyStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'skipped';
  icon: React.ComponentType<any>;
  estimatedTime: string;
  department?: string;
  assignedStaff?: string;
  completedAt?: string;
  notes?: string;
}

interface PatientJourneyData {
  patientId: string;
  patientName: string;
  currentStep: number;
  totalSteps: number;
  journeyStarted: string;
  estimatedCompletion: string;
  steps: PatientJourneyStep[];
  currentLocation: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export function PatientJourney() {
  const [selectedPatient, setSelectedPatient] = useState<string>('');
  const [journeyData, setJourneyData] = useState<PatientJourneyData | null>(null);
  const [loading, setLoading] = useState(false);
  const [showStepDetails, setShowStepDetails] = useState<string | null>(null);

  // Mock patient journey data
  const mockJourneyData: PatientJourneyData = {
    patientId: 'PT001234',
    patientName: 'John Doe',
    currentStep: 3,
    totalSteps: 8,
    journeyStarted: new Date().toISOString(),
    estimatedCompletion: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
    currentLocation: 'Consultation Room 2',
    priority: 'medium',
    steps: [
      {
        id: 'registration',
        title: 'Patient Registration',
        description: 'Complete patient registration and verify insurance',
        status: 'completed',
        icon: UserPlus,
        estimatedTime: '10 min',
        department: 'Front Desk',
        assignedStaff: 'Lisa Anderson',
        completedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        notes: 'Registration completed successfully. Insurance verified.'
      },
      {
        id: 'appointment',
        title: 'Appointment Check-in',
        description: 'Check-in for scheduled appointment',
        status: 'completed',
        icon: Calendar,
        estimatedTime: '5 min',
        department: 'Front Desk',
        assignedStaff: 'Lisa Anderson',
        completedAt: new Date(Date.now() - 2.5 * 60 * 60 * 1000).toISOString(),
        notes: 'Patient checked in on time.'
      },
      {
        id: 'triage',
        title: 'Triage Assessment',
        description: 'Initial assessment and vital signs collection',
        status: 'completed',
        icon: Activity,
        estimatedTime: '15 min',
        department: 'Triage',
        assignedStaff: 'Nurse Mary Wilson',
        completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        notes: 'Vital signs recorded. Patient stable.'
      },
      {
        id: 'consultation',
        title: 'Doctor Consultation',
        description: 'Medical examination and diagnosis',
        status: 'in-progress',
        icon: Stethoscope,
        estimatedTime: '30 min',
        department: 'Cardiology',
        assignedStaff: 'Dr. Sarah Johnson',
        notes: 'Consultation in progress.'
      },
      {
        id: 'diagnosis',
        title: 'Diagnosis & Treatment Plan',
        description: 'Finalize diagnosis and create treatment plan',
        status: 'pending',
        icon: FileText,
        estimatedTime: '15 min',
        department: 'Cardiology',
        assignedStaff: 'Dr. Sarah Johnson'
      },
      {
        id: 'pharmacy',
        title: 'Pharmacy Services',
        description: 'Prescription processing and medication dispensing',
        status: 'pending',
        icon: Pill,
        estimatedTime: '20 min',
        department: 'Pharmacy',
        assignedStaff: 'Pharmacist'
      },
      {
        id: 'billing',
        title: 'Billing & Payment',
        description: 'Process billing and collect payment',
        status: 'pending',
        icon: CreditCard,
        estimatedTime: '10 min',
        department: 'Billing',
        assignedStaff: 'Billing Staff'
      },
      {
        id: 'discharge',
        title: 'Patient Discharge',
        description: 'Discharge instructions and follow-up scheduling',
        status: 'pending',
        icon: Home,
        estimatedTime: '15 min',
        department: 'Discharge',
        assignedStaff: 'Discharge Coordinator'
      }
    ]
  };

  useEffect(() => {
    if (selectedPatient) {
      fetchPatientJourney();
    }
  }, [selectedPatient]);

  const fetchPatientJourney = async () => {
    if (!selectedPatient) return;

    try {
      setLoading(true);

      // Try to fetch real journey data from API
      try {
        const response = await fetch(`/api/patient-journey/${selectedPatient}`);
        if (response.ok) {
          const data = await response.json();
          setJourneyData(data.journey);
          return;
        }
      } catch (apiError) {
        console.warn('Patient journey API not available, using mock data');
      }

      // Fallback to mock data with selected patient info
      const mockData = {
        ...mockJourneyData,
        patientId: selectedPatient,
        patientName: getPatientName(selectedPatient)
      };
      setJourneyData(mockData);

    } catch (err) {
      console.error('Error fetching patient journey:', err);
    } finally {
      setLoading(false);
    }
  };

  const getPatientName = (patientId: string) => {
    // In a real implementation, this would fetch patient name from API
    const patientNames: { [key: string]: string } = {
      'PT001234': 'John Doe',
      'PT001235': 'Jane Smith',
      'PT001236': 'Michael Brown'
    };
    return patientNames[patientId] || 'Unknown Patient';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-gray-100 text-gray-600 border-gray-200';
      case 'skipped':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={20} className="text-green-600" />;
      case 'in-progress':
        return <Clock size={20} className="text-blue-600" />;
      case 'pending':
        return <Clock size={20} className="text-gray-400" />;
      case 'skipped':
        return <AlertCircle size={20} className="text-yellow-600" />;
      default:
        return <Clock size={20} className="text-gray-400" />;
    }
  };

  const updateStepStatus = async (stepId: string, newStatus: string, notes?: string) => {
    if (!journeyData) return;

    const updatedSteps = journeyData.steps.map(step => {
      if (step.id === stepId) {
        return {
          ...step,
          status: newStatus as any,
          completedAt: newStatus === 'completed' ? new Date().toISOString() : step.completedAt,
          notes: notes || step.notes
        };
      }
      return step;
    });

    // Update current step
    const currentStepIndex = updatedSteps.findIndex(step => step.status === 'in-progress');
    const newCurrentStep = currentStepIndex >= 0 ? currentStepIndex + 1 : journeyData.currentStep;

    setJourneyData({
      ...journeyData,
      steps: updatedSteps,
      currentStep: newCurrentStep
    });

    // Here you would make an API call to update the backend
    console.log('Updating step:', stepId, 'to status:', newStatus);
  };

  const calculateProgress = () => {
    if (!journeyData) return 0;
    const completedSteps = journeyData.steps.filter(step => step.status === 'completed').length;
    return (completedSteps / journeyData.totalSteps) * 100;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Journey</h1>
          <p className="text-gray-600 mt-1">Track complete patient flow from registration to discharge</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
          <Plus size={20} />
          <span>New Patient Journey</span>
        </button>
      </div>

      {/* Patient Selection */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Patient</h3>
        <div className="flex space-x-4">
          <select
            value={selectedPatient}
            onChange={(e) => setSelectedPatient(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a patient to track their journey...</option>
            <option value="PT001234">John Doe - PT001234</option>
            <option value="PT001235">Jane Smith - PT001235</option>
            <option value="PT001236">Michael Brown - PT001236</option>
          </select>
          <button
            onClick={fetchPatientJourney}
            disabled={!selectedPatient || loading}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <Eye size={16} />
                <span>Load Journey</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Journey Overview */}
      {journeyData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{journeyData.patientName}</h3>
              <p className="text-gray-600">Patient ID: {journeyData.patientId}</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(journeyData.priority)}`}>
                {journeyData.priority.charAt(0).toUpperCase() + journeyData.priority.slice(1)} Priority
              </span>
              <div className="text-right">
                <p className="text-sm text-gray-600">Current Location</p>
                <p className="font-medium text-gray-900">{journeyData.currentLocation}</p>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Journey Progress</span>
              <span className="text-sm text-gray-600">
                Step {journeyData.currentStep} of {journeyData.totalSteps}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${calculateProgress()}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Started: {new Date(journeyData.journeyStarted).toLocaleTimeString()}</span>
              <span>Est. Completion: {new Date(journeyData.estimatedCompletion).toLocaleTimeString()}</span>
            </div>
          </div>

          {/* Journey Steps */}
          <div className="space-y-4">
            {journeyData.steps.map((step, index) => {
              const IconComponent = step.icon;
              const isActive = step.status === 'in-progress';
              const isCompleted = step.status === 'completed';
              
              return (
                <div
                  key={step.id}
                  className={`relative p-4 rounded-lg border-2 transition-all ${
                    isActive ? 'border-blue-300 bg-blue-50' : 
                    isCompleted ? 'border-green-300 bg-green-50' : 
                    'border-gray-200 bg-white'
                  }`}
                >
                  {/* Connection Line */}
                  {index < journeyData.steps.length - 1 && (
                    <div className="absolute left-8 top-16 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Step Icon */}
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      isCompleted ? 'bg-green-100' : 
                      isActive ? 'bg-blue-100' : 
                      'bg-gray-100'
                    }`}>
                      <IconComponent size={20} className={
                        isCompleted ? 'text-green-600' : 
                        isActive ? 'text-blue-600' : 
                        'text-gray-400'
                      } />
                    </div>

                    {/* Step Content */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{step.title}</h4>
                          <p className="text-sm text-gray-600">{step.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="text-xs text-gray-500">
                              <Clock size={12} className="inline mr-1" />
                              {step.estimatedTime}
                            </span>
                            {step.department && (
                              <span className="text-xs text-gray-500">
                                <User size={12} className="inline mr-1" />
                                {step.department}
                              </span>
                            )}
                            {step.assignedStaff && (
                              <span className="text-xs text-gray-500">
                                {step.assignedStaff}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {/* Status Badge */}
                          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(step.status)}`}>
                            {getStatusIcon(step.status)}
                            <span className="ml-1">{step.status.replace('-', ' ')}</span>
                          </span>

                          {/* Action Buttons */}
                          {step.status === 'in-progress' && (
                            <div className="flex space-x-1">
                              <button
                                onClick={() => updateStepStatus(step.id, 'completed')}
                                className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                              >
                                Complete
                              </button>
                              <button
                                onClick={() => setShowStepDetails(step.id)}
                                className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                              >
                                <Edit size={12} />
                              </button>
                            </div>
                          )}

                          {step.status === 'pending' && index === journeyData.currentStep && (
                            <button
                              onClick={() => updateStepStatus(step.id, 'in-progress')}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                            >
                              Start
                            </button>
                          )}

                          <button
                            onClick={() => setShowStepDetails(showStepDetails === step.id ? null : step.id)}
                            className="text-gray-400 hover:text-gray-600 p-1"
                          >
                            <Eye size={16} />
                          </button>
                        </div>
                      </div>

                      {/* Step Details */}
                      {showStepDetails === step.id && (
                        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Department:</span>
                              <span className="ml-2 text-gray-600">{step.department || 'N/A'}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Assigned Staff:</span>
                              <span className="ml-2 text-gray-600">{step.assignedStaff || 'N/A'}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Estimated Time:</span>
                              <span className="ml-2 text-gray-600">{step.estimatedTime}</span>
                            </div>
                            {step.completedAt && (
                              <div>
                                <span className="font-medium text-gray-700">Completed At:</span>
                                <span className="ml-2 text-gray-600">
                                  {new Date(step.completedAt).toLocaleString()}
                                </span>
                              </div>
                            )}
                          </div>
                          {step.notes && (
                            <div className="mt-3">
                              <span className="font-medium text-gray-700">Notes:</span>
                              <p className="text-gray-600 mt-1">{step.notes}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {journeyData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Activity className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Record Vitals</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FileText className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Add Notes</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Pill className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Prescribe</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Calendar className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Schedule Follow-up</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
